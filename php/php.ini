[PHP]

;;;;;;;;;;;;;;;;;;;
; Resource Limits ;
;;;;;;;;;;;;;;;;;;;

max_execution_time = 90
max_input_time = 90
memory_limit = 512M
upload_max_filesize = 32M
max_file_uploads = 20
post_max_size = 32M
max_input_vars = 2000

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; Error handling and logging ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

error_reporting = E_ALL & ~E_NOTICE | E_DEPRECATED
display_errors = Off
display_startup_errors = Off
log_errors = On
log_errors_max_len = 1024
ignore_repeated_errors = Off
ignore_repeated_source = Off
report_memleaks = On
track_errors = Off
html_errors = Off
variables_order = "GPCS"
request_order = "GP"
auto_globals_jit = On
default_charset = "UTF-8"
enable_dl = Off
file_uploads = On


always_populate_raw_post_data = -1
short_open_tag = Off
precision = 14
date.timezone = "UTC"
zend.enable_gc = On

expose_php = Off

realpath_cache_size = 4M
realpath_cache_ttl = 7200
default_socket_timeout = 90
output_buffering = 4096


allow_url_fopen = On
allow_url_include = Off

[opcache]
opcache.memory_consumption=512MB
opcache.max_accelerated_files=65406
opcache.interned_strings_buffer=12
opcache.consistency_checks=0
opcache.validate_timestamps=0
opcache.enable_cli=1
opcache.enable_file_override=1
opcache.save_comments=1

[CLI Server]
cli_server.color = On

[Pdo_mysql]
pdo_mysql.cache_size = 2000

[Session]
session.save_handler = files
session.use_strict_mode = 0
session.use_cookies = 1
session.use_only_cookies = 1
session.name = PHPSESSID
session.auto_start = 0
session.cookie_lifetime = 0
session.cookie_path = /

[apcu]
apc.enabled = 1
apc.enabled_cli = 1

[openssl]
openssl.cafile = /etc/ssl/certs/ca-certificates.crt
openssl.capath = /etc/ssl/certs

[curl]
curl.cainfo = /etc/ssl/certs/ca-bundle.crt