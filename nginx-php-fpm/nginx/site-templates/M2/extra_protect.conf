# Deny files that only for internal use (work with ssh or ftp to download)
location ~ ^/\.(svn|git|hg|htpasswd|bash|ssh|php_cs|config) { deny all; }
location ~ ^/([^/])+\.(sh|pl|py|lua|inc|swp|phar|php_|log|ini|md|sql|conf|yml|zip|tar|.+gz)$ { deny all; }

# Deny access per default .htaccess rules
location ~ /media/(customer/|downloadable/|import/|theme_customization/.*\.xml) { deny all; }
location ~* ^/(opt|media|static|\.well-known)/.*\.php$ { deny all; }
location ~ /errors/.*\.xml { deny all; }
location ~ Gruntfile\.js { deny all; }

# Deny cron and files with the obvious names. favorite entry points for hackers and script kiddie
location ~* ^/(cron|phpminiadmin|pma|sqlyog|adminer.+)\.php { deny all; }

# Deny auth and composer
location ~ (auth|package|composer)\.(json|lock)$ { deny all; }

# Limit requests and return "GONE 410" for search bots (if you need something more - use firewall/iptables)
location ~ ^/(fire|one.+)?checkout/  {
        limit_req zone=checkout burst=8;
        limit_req_status 429;
        if ($http_user_agent ~* "Baiduspider|Googlebot|bingbot|rogerbot|Yahoo|Yandex") { return 410; }
        try_files $uri $uri/ /index.php$is_args$args;
        }

location ~ ^/(wishlist|customer|catalog/product_compare|tag/product/list|sales/guest/view)/  {
        limit_req zone=catalog burst=8;
        limit_req_status 429;
        if ($http_user_agent ~* "Baiduspider|Googlebot|bingbot|rogerbot|Yahoo|Yandex") { return 410; }
        try_files $uri $uri/ /index.php$is_args$args;
        }

location ~* ^/((catalog)?search/ajax.+ges?t|newsletter|sendfriend|contact/index/post|review/product/post)/ {
        limit_req zone=search_and_post burst=4;
        limit_req_status 429;
        if ($http_user_agent ~* "Baiduspider|Googlebot|bingbot|rogerbot|Yahoo|Yandex") { return 410; }
        try_files $uri $uri/ /index.php$is_args$args;
        }

## Remove "index.php" in url
#if ($request_uri !~ "/(ADMIN_PLACEHOLDER|PMA_PLACEHOLDER)/") {set $no_index_php A; }
#if ($request_uri ~* "^(.*/)index\.php(/?)(.*)") { set $no_index_php "${no_index_php}B"; }
#if ($no_index_php = AB) { return 301 $1$3; }

set $filters "";
## Dont let search bots to drill your website | exclude some args from indexing
if ($args ~ ^(brand|cat|color|dir|from|limit|price|type|mode|size|manufacturer|product_list_mode|product_list_order|product_list_dir)=.+) { set $filters A; }
if ($http_user_agent ~* "Baiduspider|Googlebot|bingbot|Yahoo|Yandex") { set $filters "${filters}B"; }
if ($filters = AB) { return 410; }

## Wordpress files and locations protection
location ~ /wp-config\.php { deny all; }
location ~ /wp-includes/(.*)\.php { deny all; }
location ~ /wp-admin/includes(.*)$ { deny all; }
location ~ /xmlrpc\.php { deny all; }
location ~ /wp-content/uploads/(.*)\.php(.?) { deny all; }