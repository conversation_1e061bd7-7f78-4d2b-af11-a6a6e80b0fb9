upstream fastcgi_backend {
    server   unix:/var/run/php-fpm.sock;
}


server {
    listen                      80 default_server;
    listen                      443 default_server http2 ssl;

    client_max_body_size        10M;

    ssl_certificate            /etc/nginx/ssl/cert.crt;
    ssl_certificate_key        /etc/nginx/ssl/cert.key;

    server_name ${DOMAIN};

    set $MAGENTO_ROOT ${MAGENTO_ROOT};
    set $MAGE_MODE production;
    set $MAGE_RUN_TYPE ${MAGE_RUN_TYPE};
    set $MAGE_RUN_CODE ${MAGE_RUN_CODE};

    include /etc/nginx/conf.d/includes/default/60-spider.conf;
    include /etc/nginx/conf.d/includes/default/70-staticcaching.conf;
    include /etc/nginx/conf.d/M2/*.conf;
}