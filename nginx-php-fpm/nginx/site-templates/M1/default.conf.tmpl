server {
    listen                      80 default_server;
    listen                      443 default_server http2 ssl;
    client_max_body_size        10M;

    server_name ${DOMAIN};

    ssl_certificate            /etc/nginx/ssl/cert.crt;
    ssl_certificate_key        /etc/nginx/ssl/cert.key;

    set $MAGENTO_ROOT ${MAGENTO_ROOT};
    set $MAGE_RUN_TYPE ${MAGE_RUN_TYPE};
    set $MAGE_RUN_CODE ${MAGE_RUN_CODE};

    root                        ${MAGENTO_ROOT};
    index                       index.php;

    include /etc/nginx/conf.d/includes/default/*.conf;
    include /etc/nginx/conf.d/includes/magento/*.conf;

    include /etc/nginx/conf.d/M1/*.conf;

    location ~ \.php$ {
        include /etc/nginx/conf.d/includes/php/*.conf;
    }
}