user                www-data;
worker_processes    auto;
worker_rlimit_nofile 100000;

error_log           /var/log/nginx/error.log warn;
pid                 /var/run/nginx.pid;

events {
    worker_connections  4096;
    multi_accept on;
    use epoll;
}

http {

    include /etc/nginx/conf.d/includes/nginx/*.conf;
    include /etc/nginx/conf.d/includes/addons/*.conf;

    # Sites
    include /etc/nginx/conf.d/*.conf;
}


daemon off;