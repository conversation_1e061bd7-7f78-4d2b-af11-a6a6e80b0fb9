# Only include if media is stored in database
# location ^~ /media/ {
#    try_files $uri $uri/ /get.php$is_args$args;
#}

location ^~ /app/                       { deny all; log_not_found off; }
location ^~ /includes/                  { deny all; log_not_found off; }
location ^~ /lib/                       { deny all; log_not_found off; }
location ^~ /shell/                     { deny all; log_not_found off; }
location ^~ /media/downloadable/        { deny all; log_not_found off; }
location ^~ /media/customer/            { deny all; log_not_found off; }
location ^~ /pkginfo/                   { deny all; log_not_found off; }
location ^~ /report/config.xml          { deny all; log_not_found off; }
location ^~ /var/                       { deny all; log_not_found off; }
location ^~ /downloader/                { deny all; log_not_found off; }
location ^~ /dev/                       { deny all; log_not_found off; }
location ^~ /tests/                     { deny all; log_not_found off; }

location ~ /cron\.php                   { deny all; }

location ~ /errors/.*\.(xml|phtml)      { deny all; log_not_found off; }

# deny htaccess files
location ~ /\. {
    deny  all;
    access_log off;
    log_not_found off;
}

## Extra protection

location ~ /(dev/tests/|errors/local.xml|cron\.php) { deny all; }

location ~ ^/.*\.(sh|pl|swp|phar|sql|conf|zip|tar|.+gz)$ { return 444; }

location ~ /\.(svn|git|hg|htpasswd|bash|ssh) { return 444; }

location ~* /(lib|media|shell|skin)/.*\.php$ { deny all; }

location ~ /(wishlist|customer|contact|review|catalogsearch|newsletter|(fire|one.+)?checkout)/  {
    limit_req zone=general burst=5;
    limit_req_status 429;
    if ($http_user_agent ~* "Baiduspider|Googlebot|bingbot|Yahoo|YandexBot") { return 410; }
    try_files $uri $uri/ @handler;
}