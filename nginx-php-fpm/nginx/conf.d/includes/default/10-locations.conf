location = /js/index.php/x.js {
   rewrite ^(.*\.php)/ $1 last;
   }

location / {
    index index.html index.php;
    # auth_basic "Restricted"; #For Basic Auth
    # auth_basic_user_file /etc/nginx/.htpasswd;  #For Basic Auth
    try_files /maintenance.html $uri $uri/ @handler;
    expires 30d;
}

location /robots.txt {
    allow all;
    log_not_found off;
    access_log off;
}

location /favicon.ico {
    allow all;
    log_not_found off;
    access_log off;
}