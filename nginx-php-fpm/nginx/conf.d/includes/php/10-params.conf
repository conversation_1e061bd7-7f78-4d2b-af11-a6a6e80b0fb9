fastcgi_index                   index.php;


fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;

fastcgi_param MAGENTO_ROOT ${MAGENTO_ROOT};
fastcgi_param MAGE_RUN_TYPE ${MAGE_RUN_TYPE};
fastcgi_param MAGE_RUN_CODE ${MAGE_RUN_CODE};

include fastcgi_params;

 ## Tweak fastcgi buffers, just in case.
fastcgi_buffer_size 128k;
fastcgi_buffers 1024 4k;
fastcgi_busy_buffers_size 256k;
fastcgi_temp_file_write_size 256k;
fastcgi_read_timeout 600s;
fastcgi_connect_timeout 600s;
fastcgi_keep_conn on;
proxy_read_timeout 600s;

add_header X-Config-By 'CopeX.io';
add_header X-UA-Compatible 'IE=Edge,chrome=1';
add_header X-Processing-Time $request_time;
add_header X-Pool 'default';